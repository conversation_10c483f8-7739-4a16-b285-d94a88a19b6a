import 'package:flutter/material.dart';
import 'package:flutter_callkit_incoming/flutter_callkit_incoming.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:livekit_client/livekit_client.dart';
import 'package:livekit_components/livekit_components.dart'
    show RoomContext, TranscriptionBuilder;
import 'package:provider/provider.dart';
import 'control_bar.dart';
import './services/token_service.dart';

/// The main voice assistant screen that manages the LiveKit room connection
/// and displays the status visualizer and control bar
class CallingScreen extends StatefulWidget {
  const CallingScreen({super.key});
  @override
  State<CallingScreen> createState() => _CallingScreenState();
}

class _CallingScreenState extends State<CallingScreen> {
  // Create a LiveKit Room instance with audio visualization enabled
  // This is the main object that manages the connection to LiveKit
  final room = Room(roomOptions: const RoomOptions(enableVisualizer: true));

  @override
  void dispose() {
    super.dispose();
    FlutterCallkitIncoming.endAllCalls();
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      // Provide the TokenService and RoomContext to descendant widgets
      // TokenService handles LiveKit authentication
      // RoomContext provides LiveKit room state and operations
      providers: [
        ChangeNotifierProvider(create: (context) => TokenService()),
        ChangeNotifierProvider(create: (context) => RoomContext(room: room)),
      ],
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Voice Assistant'),
        ),
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Control bar handles room connection and audio controls
                const Expanded(
                  flex: 3,
                  child: ControlBar(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
