name: flutter_callkit_incoming_example
description: Demonstrates how to use the flutter_callkit_incoming plugin.

publish_to: "none"

environment:
  sdk: ">=2.18.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter

  flutter_callkit_incoming:
    path: ../

  firebase_core: ^2.12.0
  firebase_messaging: ^14.6.0
  http:
  uuid:
  clipboard: ^0.1.3
  onesignal_flutter:
  permission_handler: ^11.0.1

  #livekit
  livekit_components: ^1.1.1  # Compatible with flutter_webrtc 0.12.8
  flutter_webrtc: 0.12.8  # Force exact compatible version

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons:
  chat_bubbles:
  livekit_client: ^2.3.6  # Compatible version with livekit_components 1.1.1
  flutter_dotenv:
  provider:

dev_dependencies:

flutter:
  uses-material-design: true

  assets:
    - assets/test.png
