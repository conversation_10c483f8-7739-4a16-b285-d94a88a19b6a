# Uncomment this line to define a global platform for your project
platform :ios, '13.0'

# CocoaPods analytics sends network stats synchronously affecting flutter build latency.
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  unless File.exist?(generated_xcode_build_settings_path)
    raise "#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure flutter pub get is executed first"
  end

  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT\=(.*)/)
    return matches[1].strip if matches
  end
  raise "FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get"
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)

flutter_ios_podfile_setup

target 'Runner' do
  use_frameworks!
  use_modular_headers!

  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))
end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    flutter_additional_ios_build_settings(target)
    target.build_configurations.each do |config|
      xcconfig_path = config.base_configuration_reference.real_path
      xcconfig = File.read(xcconfig_path)
      xcconfig_mod = xcconfig.gsub(/DT_TOOLCHAIN_DIR/, "TOOLCHAIN_DIR")
      File.open(xcconfig_path, "w") { |file| file << xcconfig_mod }
      config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= [
              '$(inherited)',



              ## dart: PermissionGroup.camera
              'PERMISSION_CAMERA=1',

              ## dart: PermissionGroup.microphone
              'PERMISSION_MICROPHONE=1',

              ## dart: PermissionGroup.speech
              'PERMISSION_SPEECH_RECOGNIZER=1',

              ## dart: PermissionGroup.photos
              'PERMISSION_PHOTOS=1',

              ## The 'PERMISSION_LOCATION' macro enables the `locationWhenInUse` and `locationAlways` permission. If
              ## the application only requires `locationWhenInUse`, only specify the `PERMISSION_LOCATION_WHENINUSE`
              ## macro.
              ##
              ## dart: [PermissionGroup.location, PermissionGroup.locationAlways, PermissionGroup.locationWhenInUse]
              'PERMISSION_LOCATION=1',
              'PERMISSION_LOCATION_WHENINUSE=0',

              ## dart: PermissionGroup.notification
              'PERMISSION_NOTIFICATIONS=1',

              ## dart: PermissionGroup.mediaLibrary
              'PERMISSION_MEDIA_LIBRARY=1',

              ## dart: PermissionGroup.sensors
              'PERMISSION_SENSORS=1',

              ## dart: PermissionGroup.bluetooth
              'PERMISSION_BLUETOOTH=1',

              ## dart: PermissionGroup.appTrackingTransparency
              'PERMISSION_APP_TRACKING_TRANSPARENCY=1',

              ## dart: PermissionGroup.criticalAlerts
              'PERMISSION_CRITICAL_ALERTS=1',

              ## dart: PermissionGroup.criticalAlerts
              'PERMISSION_ASSISTANT=1',
            ]
    end
  end
end
